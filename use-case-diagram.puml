@startuml DPMPTSP_Chatbot_Use_Case_Diagram

!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E1F5FE
    BorderColor #0277BD
}
skinparam usecase {
    BackgroundColor #F3E5F5
    BorderColor #7B1FA2
}
skinparam package {
    BackgroundColor #E8F5E8
    BorderColor #388E3C
}

title DPMPTSP Virtual Assistant Chatbot - Use Case Diagram

' Actors
actor "Citizen" as citizen
actor "Business Owner" as business
actor "Investor" as investor
actor "DPMPTSP Admin" as admin
actor "Government Staff" as staff

' External Systems
actor "Gemini AI" as gemini
actor "Database" as db

' Use Case Packages
package "Chat Management" {
    usecase "Create Chat Session" as UC1
    usecase "Join Chat" as UC2
    usecase "Send Message" as UC3
    usecase "Receive AI Response" as UC4
    usecase "View Chat History" as UC5
    usecase "Reset Chat" as UC6
    usecase "Show Typing Indicator" as UC7
}

package "Information Services" {
    usecase "Ask About Licensing" as UC8
    usecase "Ask About Investment" as UC9
    usecase "Get Service Information" as UC10
    usecase "Get Contact Information" as UC11
    usecase "Receive Welcome Message" as UC12
}

package "Document Management" {
    usecase "Upload PDF Document" as UC13
    usecase "List PDF Documents" as UC14
    usecase "Delete PDF Document" as UC15
    usecase "Process PDF Content" as UC16
    usecase "Search PDF Content" as UC17
}

package "AI Processing" {
    usecase "Generate Context-Aware Response" as UC18
    usecase "Search Vector Database" as UC19
    usecase "Extract PDF Text" as UC20
    usecase "Create Document Chunks" as UC21
}

package "System Administration" {
    usecase "Monitor System Performance" as UC22
    usecase "Manage Rate Limiting" as UC23
    usecase "Handle Error Recovery" as UC24
    usecase "Maintain Database" as UC25
}

' Primary User Relationships
citizen --> UC1 : creates
citizen --> UC2 : joins
citizen --> UC3 : sends
citizen --> UC4 : receives
citizen --> UC5 : views
citizen --> UC6 : resets
citizen --> UC7 : shows
citizen --> UC8 : asks
citizen --> UC10 : requests
citizen --> UC11 : requests
citizen --> UC12 : receives

business --> UC1
business --> UC2
business --> UC3
business --> UC4
business --> UC5
business --> UC8
business --> UC9 : asks
business --> UC10
business --> UC11

investor --> UC1
investor --> UC2
investor --> UC3
investor --> UC4
investor --> UC5
investor --> UC9
investor --> UC10
investor --> UC11

' Admin Relationships
admin --> UC13 : uploads
admin --> UC14 : views
admin --> UC15 : deletes
admin --> UC22 : monitors
admin --> UC25 : maintains

staff --> UC13
staff --> UC14
staff --> UC15
staff --> UC22

' System Relationships
UC3 --> UC18 : triggers
UC18 --> UC19 : uses
UC13 --> UC16 : triggers
UC16 --> UC20 : includes
UC16 --> UC21 : includes
UC19 --> UC17 : searches

' External System Relationships
UC18 --> gemini : uses
UC4 --> gemini : generated by
UC1 --> db : stores in
UC3 --> db : saves to
UC5 --> db : retrieves from
UC13 --> db : stores in
UC19 --> db : queries

' Include Relationships
UC8 ..> UC17 : <<include>>
UC9 ..> UC17 : <<include>>
UC10 ..> UC17 : <<include>>
UC4 ..> UC18 : <<include>>

' Extend Relationships
UC11 ..> UC4 : <<extend>>
UC24 ..> UC4 : <<extend>>
UC23 ..> UC3 : <<extend>>

' Notes
note right of UC18
  Generates responses based on:
  - Chat history
  - PDF document context
  - DPMPTSP knowledge base
  - Indonesian language
end note

note right of UC17
  Vector-based semantic search
  for relevant PDF content
end note

note bottom of admin
  Manages official government
  documents and regulations
end note

note bottom of citizen
  Seeks information about
  government services
end note

@enduml
